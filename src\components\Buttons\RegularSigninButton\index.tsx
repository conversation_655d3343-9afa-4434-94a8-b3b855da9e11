'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { Button, Modal, TextInput, PasswordInput, Text, Alert, Anchor } from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconMail, IconLock, IconAlertCircle, IconCheck } from '@tabler/icons-react';
import styles from './index.module.scss';

interface SigninFormData {
  email: string;
  password: string;
}

interface SignupFormData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
}

interface ForgotPasswordFormData {
  email: string;
}

export const RegularSigninButton = () => {
  const [modalType, setModalType] = useState<'signin' | 'signup' | 'forgot' | null>(null);
  const [loading, setLoading] = useState(false);

  const signinForm = useForm<SigninFormData>({
    initialValues: {
      email: '',
      password: '',
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
      password: (value) => (value.length < 1 ? 'Password is required' : null),
    },
  });

  const signupForm = useForm<SignupFormData>({
    initialValues: {
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
      password: (value) => {
        if (value.length < 8) return 'Password must be at least 8 characters';
        if (!/(?=.*[a-z])/.test(value)) return 'Password must contain lowercase letter';
        if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain uppercase letter';
        if (!/(?=.*\d)/.test(value)) return 'Password must contain a number';
        if (!/(?=.*[@$!%*?&])/.test(value)) return 'Password must contain special character';
        return null;
      },
      confirmPassword: (value, values) =>
        value !== values.password ? 'Passwords do not match' : null,
      fullName: (value) => (value.length < 1 ? 'Full name is required' : null),
    },
  });

  const forgotForm = useForm<ForgotPasswordFormData>({
    initialValues: {
      email: '',
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
    },
  });

  const handleSignin = async (values: SigninFormData) => {
    setLoading(true);
    try {
      const result = await signIn('credentials', {
        email: values.email,
        password: values.password,
        redirect: false,
      });

      if (result?.error) {
        notifications.show({
          title: 'Sign In Failed',
          message: 'Invalid email or password. Please check your credentials and try again.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      } else {
        notifications.show({
          title: 'Welcome back!',
          message: 'You have been signed in successfully.',
          color: 'green',
          icon: <IconCheck size={16} />,
        });
        setModalType(null);
        signinForm.reset();
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async (values: SignupFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: values.email,
          password: values.password,
          fullName: values.fullName,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        notifications.show({
          title: 'Account Created!',
          message: 'Please check your email to verify your account before signing in.',
          color: 'green',
          icon: <IconCheck size={16} />,
          autoClose: 8000,
        });
        setModalType(null);
        signupForm.reset();
      } else {
        notifications.show({
          title: 'Signup Failed',
          message: data.error || 'Failed to create account. Please try again.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (values: ForgotPasswordFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: values.email,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        notifications.show({
          title: 'Reset Link Sent',
          message: 'If an account exists, you will receive a password reset link.',
          color: 'blue',
          icon: <IconMail size={16} />,
          autoClose: 8000,
        });
        setModalType(null);
        forgotForm.reset();
      } else {
        notifications.show({
          title: 'Error',
          message: data.error || 'Failed to send reset link. Please try again.',
          color: 'red',
          icon: <IconAlertCircle size={16} />,
        });
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred. Please try again.',
        color: 'red',
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setLoading(false);
    }
  };

  const closeModal = () => {
    setModalType(null);
    signinForm.reset();
    signupForm.reset();
    forgotForm.reset();
  };

  return (
    <>
      <Button
        className={styles.regular_button}
        onClick={() => setModalType('signin')}
        leftSection={<IconMail size={16} />}
      >
        Sign in with Email
      </Button>

      {/* Sign In Modal */}
      <Modal
        opened={modalType === 'signin'}
        onClose={closeModal}
        title="Sign In"
        centered
        size="sm"
      >
        <form onSubmit={signinForm.onSubmit(handleSignin)}>
          <TextInput
            label="Email"
            placeholder="<EMAIL>"
            required
            leftSection={<IconMail size={16} />}
            {...signinForm.getInputProps('email')}
            mb="md"
          />
          <PasswordInput
            label="Password"
            placeholder="Your password"
            required
            leftSection={<IconLock size={16} />}
            {...signinForm.getInputProps('password')}
            mb="md"
          />
          
          <Button type="submit" fullWidth loading={loading} mb="sm">
            Sign In
          </Button>
          
          <Text size="sm" ta="center">
            Don't have an account?{' '}
            <Anchor component="button" type="button" onClick={() => setModalType('signup')}>
              Sign up
            </Anchor>
          </Text>
          
          <Text size="sm" ta="center" mt="xs">
            <Anchor component="button" type="button" onClick={() => setModalType('forgot')}>
              Forgot password?
            </Anchor>
          </Text>
        </form>
      </Modal>

      {/* Sign Up Modal */}
      <Modal
        opened={modalType === 'signup'}
        onClose={closeModal}
        title="Create Account"
        centered
        size="sm"
      >
        <Alert icon={<IconCheck size={16} />} color="blue" mb="md">
          Get 2000 bonus points when you sign up!
        </Alert>
        
        <form onSubmit={signupForm.onSubmit(handleSignup)}>
          <TextInput
            label="Full Name"
            placeholder="Your full name"
            required
            {...signupForm.getInputProps('fullName')}
            mb="md"
          />
          <TextInput
            label="Email"
            placeholder="<EMAIL>"
            required
            leftSection={<IconMail size={16} />}
            {...signupForm.getInputProps('email')}
            mb="md"
          />
          <PasswordInput
            label="Password"
            placeholder="Create a strong password"
            required
            leftSection={<IconLock size={16} />}
            {...signupForm.getInputProps('password')}
            mb="md"
          />
          <PasswordInput
            label="Confirm Password"
            placeholder="Confirm your password"
            required
            leftSection={<IconLock size={16} />}
            {...signupForm.getInputProps('confirmPassword')}
            mb="md"
          />
          
          <Button type="submit" fullWidth loading={loading} mb="sm">
            Create Account
          </Button>
          
          <Text size="sm" ta="center">
            Already have an account?{' '}
            <Anchor component="button" type="button" onClick={() => setModalType('signin')}>
              Sign in
            </Anchor>
          </Text>
        </form>
      </Modal>

      {/* Forgot Password Modal */}
      <Modal
        opened={modalType === 'forgot'}
        onClose={closeModal}
        title="Reset Password"
        centered
        size="sm"
      >
        <Text size="sm" c="dimmed" mb="md">
          Enter your email address and we'll send you a link to reset your password.
        </Text>
        
        <form onSubmit={forgotForm.onSubmit(handleForgotPassword)}>
          <TextInput
            label="Email"
            placeholder="<EMAIL>"
            required
            leftSection={<IconMail size={16} />}
            {...forgotForm.getInputProps('email')}
            mb="md"
          />
          
          <Button type="submit" fullWidth loading={loading} mb="sm">
            Send Reset Link
          </Button>
          
          <Text size="sm" ta="center">
            Remember your password?{' '}
            <Anchor component="button" type="button" onClick={() => setModalType('signin')}>
              Sign in
            </Anchor>
          </Text>
        </form>
      </Modal>
    </>
  );
};

"use client"

import { 
  Container, 
  Paper, 
  Title, 
  Text, 
  Stack, 
  Button, 
  Divider, 
  Group,
  Box,
  Center,
  Transition,
  ThemeIcon,
  Alert
} from "@mantine/core"
import { 
  IconBrandGoogle, 
  IconBrandFacebook, 
  IconMail,
  IconGift,
  IconCoins,
  IconAlertCircle
} from "@tabler/icons-react"
import { useState, useEffect } from "react"
import { signIn, getProviders } from "next-auth/react"
import { useSearchParams } from "next/navigation"
import { SIGNUP_POINT, USD_POINT_RATE, APP_VERSION } from "../../lib/config"
import Link from "next/link"
import styles from "./index.module.css"

interface Provider {
  id: string
  name: string
  type: string
  signinUrl: string
  callbackUrl: string
}

const getProviderIcon = (providerId: string) => {
  switch (providerId) {
    case 'google':
      return <IconBrandGoogle size={20} />
    case 'facebook':
      return <IconBrandFacebook size={20} />
    default:
      return <IconMail size={20} />
  }
}

const getProviderColor = (providerId: string) => {
  switch (providerId) {
    case 'google':
      return 'red'
    case 'facebook':
      return 'blue'
    default:
      return 'gray'
  }
}

export const LoginPage = () => {
  const [providers, setProviders] = useState<Record<string, Provider> | null>(null)
  const [loading, setLoading] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)
  const searchParams = useSearchParams()
  const error = searchParams.get('error')
  // Trigger recompilation

  useEffect(() => {
    setMounted(true)
    const fetchProviders = async () => {
      const res = await getProviders()
      setProviders(res)
    }
    fetchProviders()
  }, [])

  const handleSignIn = async (providerId: string) => {
    setLoading(providerId)
    try {
      await signIn(providerId, { callbackUrl: '/' })
    } catch (error) {
      console.error('Sign in error:', error)
    } finally {
      setLoading(null)
    }
  }

  return (
    <Container size="sm" py={80} className={styles.loginContainer}>
      <Center>
        <Box w="100%" maw={450}>
          <Transition
            mounted={mounted}
            transition="slide-up"
            duration={600}
            timingFunction="ease"
          >
            {(transitionStyles) => (
              <Paper
                style={transitionStyles}
                radius="lg"
                p="xl"
                withBorder
                shadow="lg"
                className={styles.loginPaper}
              >
                <Stack gap="lg">
                  {/* Header */}
                  <Box ta="center">
                    <Title
                      order={2}
                      fw={600}
                      mb="xs"
                      className={styles.gradientTitle}
                    >
                      Welcome to ODude
                    </Title>
                    <Text c="dimmed" size="sm">
                      Sign in to access your decentralized identity
                    </Text>
                  </Box>

                  {/* Error Alert */}
                  {error && (
                    <Alert
                      icon={<IconAlertCircle size={16} />}
                      title="Authentication Error"
                      color="red"
                      variant="light"
                      className={styles.errorAlert}
                    >
                      {error === 'AccessDenied'
                        ? 'Access was denied. Please try again or contact support.'
                        : 'An error occurred during sign in. Please try again.'
                      }
                    </Alert>
                  )}

                  {/* Signup Incentive */}
                  <Paper
                    p="md"
                    radius="md"
                    className={styles.incentiveCard}
                  >
                    <Group gap="sm" justify="center">
                      <ThemeIcon variant="light" color="blue" size="sm">
                        <IconGift size={16} />
                      </ThemeIcon>
                      <Box>
                        <Text size="sm" fw={500} c="blue.9">
                          <IconCoins size={14} style={{ display: 'inline', marginRight: 4 }} />
                          Get {SIGNUP_POINT} points (${SIGNUP_POINT * USD_POINT_RATE}) instantly!
                        </Text>
                        <Text size="xs" c="dimmed">
                          Unlock features when you sign up
                        </Text>
                      </Box>
                    </Group>
                  </Paper>

                  {/* Sign In Buttons */}
                  {error !== 'AccessDenied' && (
                    <>
                      <Divider label="Sign in with" labelPosition="center" />
                      
                      <Stack gap="sm">
                        {providers &&
                          Object.values(providers).map((provider) => (
                            <Button
                              key={provider.name}
                              variant="outline"
                              size="md"
                              leftSection={getProviderIcon(provider.id)}
                              onClick={() => handleSignIn(provider.id)}
                              loading={loading === provider.id}
                              color={getProviderColor(provider.id)}
                              fullWidth
                              radius="md"
                              className={styles.signInButton}
                            >
                              Continue with {provider.name}
                            </Button>
                          ))}
                      </Stack>
                    </>
                  )}

                  <Divider />

                  {/* Footer */}
                  <Box ta="center">
                    <Text size="xs" c="dimmed" mb="xs">
                      By signing in, you agree to our{' '}
                      <Text component={Link} href="/privacy-policy" size="xs" c="blue" td="underline">
                        terms of service
                      </Text>{' '}
                      and{' '}
                      <Text component={Link} href="/privacy-policy" size="xs" c="blue" td="underline">
                        privacy policy
                      </Text>
                      .
                    </Text>
                    <Text size="xs" c="dimmed" mb="sm">
                      ODude Names are decentralized and give you full control over your data.
                    </Text>
                    <Text size="xs" c="dimmed">
                      Version {APP_VERSION}
                    </Text>
                  </Box>

                  {/* Back to Home */}
                  <Button
                    component={Link}
                    href="/"
                    variant="subtle"
                    color="gray"
                    size="sm"
                    fullWidth
                    className={styles.backButton}
                  >
                    ← Back to Home
                  </Button>
                </Stack>
              </Paper>
            )}
          </Transition>
        </Box>
      </Center>
    </Container>
  )
}

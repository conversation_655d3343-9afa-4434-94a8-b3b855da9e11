"use client"

import { 
  Container, 
  Title, 
  Text, 
  Stack, 
  Group, 
  Button, 
  Box, 
  Center,
  Transition,
  Paper,
  SimpleGrid,
  ThemeIcon,
  Anchor
} from "@mantine/core"
import { 
  IconSearch, 
  IconUserPlus, 
  IconShield, 
  IconGlobe,
  IconArrowRight,
  IconCoins,
  IconGift
} from "@tabler/icons-react"
import { ODudeNameSearch } from "../Search"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { SIGNUP_POINT, USD_POINT_RATE } from "../../lib/config"
import Link from "next/link"
import styles from "./index.module.css"

const features = [
  {
    icon: IconSearch,
    title: "Discover People",
    description: "Search and find people by their ODude names instantly"
  },
  {
    icon: IconUserPlus,
    title: "Create Contacts",
    description: "Build your network with decentralized contact management"
  },
  {
    icon: IconShield,
    title: "Secure & Private",
    description: "Your data is protected with blockchain technology"
  },
  {
    icon: IconGlobe,
    title: "Global Network",
    description: "Connect with people worldwide through ODude names"
  }
]

export const LandingPage = () => {
  const [mounted, setMounted] = useState(false)
  const router = useRouter()

  useEffect(() => {
    setMounted(true)
  }, [])

  return (
    <Box>
      {/* Hero Section */}
      <Container size="lg" py={80} className={styles.heroSection}>
        <Center>
          <Stack align="center" gap="xl" maw={600}>
            <Transition
              mounted={mounted}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
            >
              {(styles) => (
                <div style={styles}>
                  <Title
                    order={1}
                    size="3.5rem"
                    fw={300}
                    ta="center"
                    mb="md"
                    className={styles.gradientTitle}
                  >
                    ODude
                  </Title>
                </div>
              )}
            </Transition>

            <Transition
              mounted={mounted}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
              delay={200}
            >
              {(styles) => (
                <div style={styles}>
                  <Text size="xl" c="dimmed" ta="center" maw={500}>
                    Discover, connect, and manage your decentralized identity with ODude names
                  </Text>
                </div>
              )}
            </Transition>

            <Transition
              mounted={mounted}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
              delay={400}
            >
              {(styles) => (
                <Box style={styles} w="100%" maw={500} className={styles.searchBox}>
                  <ODudeNameSearch
                    size="lg"
                    placeholder="Search for an ODude name..."
                  />
                </Box>
              )}
            </Transition>

            <Transition
              mounted={mounted}
              transition="slide-up"
              duration={800}
              timingFunction="ease"
              delay={600}
            >
              {(styles) => (
                <Group style={styles} gap="md">
                  <Button
                    component={Link}
                    href="/auth/signin"
                    size="lg"
                    radius="xl"
                    variant="gradient"
                    gradient={{ from: 'blue', to: 'cyan' }}
                    leftSection={<IconUserPlus size={20} />}
                    rightSection={<IconArrowRight size={16} />}
                    className={styles.ctaButton}
                  >
                    Get Started
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    radius="xl"
                    color="gray"
                  >
                    Learn More
                  </Button>
                </Group>
              )}
            </Transition>

            {/* Signup Incentive */}
            <Transition
              mounted={mounted}
              transition="fade"
              duration={1000}
              delay={800}
            >
              {(styles) => (
                <Paper
                  style={styles}
                  p="md"
                  radius="lg"
                  withBorder
                  className={styles.incentiveCard}
                >
                  <Group gap="xs" justify="center">
                    <ThemeIcon variant="light" color="blue" size="sm">
                      <IconGift size={16} />
                    </ThemeIcon>
                    <Text size="sm" fw={500}>
                      Get {SIGNUP_POINT} points (${SIGNUP_POINT * USD_POINT_RATE}) when you sign up!
                    </Text>
                  </Group>
                </Paper>
              )}
            </Transition>
          </Stack>
        </Center>
      </Container>

      {/* Features Section */}
      <Container size="lg" py={60}>
        <Transition
          mounted={mounted}
          transition="slide-up"
          duration={800}
          delay={1000}
        >
          {(styles) => (
            <div style={styles}>
              <Title order={2} ta="center" mb="xl" fw={300}>
                Why Choose ODude?
              </Title>
              <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="xl">
                {features.map((feature, index) => (
                  <Paper
                    key={index}
                    p="xl"
                    radius="lg"
                    withBorder
                    className={styles.featureCard}
                  >
                    <Stack align="center" gap="md">
                      <ThemeIcon
                        size={60}
                        radius="xl"
                        variant="gradient"
                        gradient={{ from: 'blue', to: 'cyan' }}
                        className={styles.featureIcon}
                      >
                        <feature.icon size={30} />
                      </ThemeIcon>
                      <Title order={4} ta="center" fw={500}>
                        {feature.title}
                      </Title>
                      <Text size="sm" c="dimmed" ta="center">
                        {feature.description}
                      </Text>
                    </Stack>
                  </Paper>
                ))}
              </SimpleGrid>
            </div>
          )}
        </Transition>
      </Container>
    </Box>
  )
}
